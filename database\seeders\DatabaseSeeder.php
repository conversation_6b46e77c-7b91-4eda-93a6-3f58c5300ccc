<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Admin User
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'password' => Hash::make('password'),
                'role' => 'Admin',
            ]
        );

        // Create Test User
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => Hash::make('password'),
                'role' => 'User',
            ]
        );
    }
}
