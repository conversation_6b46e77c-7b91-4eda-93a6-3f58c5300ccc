<script setup lang="ts">
import AuthenticatedLayout from '@/layouts/AuthenticatedLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit } from 'lucide-vue-next';

interface User {
    id: number;
    name: string;
    email: string;
    role: 'Admin' | 'User';
}

interface Props {
    users: User[];
}

const props = defineProps<Props>();

const getRoleColor = (role: User['role']) => {
    switch (role) {
        case 'Admin':
            return 'bg-red-100 text-red-800 hover:bg-red-200';
        case 'User':
            return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
        default:
            return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
};
</script>

<template>
    <Head title="Users" />

    <AuthenticatedLayout>
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-2xl font-semibold">Users</h1>
                    <p class="text-gray-600">Manage system users and their roles</p>
                    <p class="text-red-500">Debug: This component is rendering.</p>
                </div>
                <Link :href="route('users.create')">
                    <Button>
                        <Plus class="mr-2 h-4 w-4" />
                        Add User
                    </Button>
                </Link>
            </div>

            <div class="bg-white rounded-lg shadow">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Role
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="user in users" :key="user.id">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="font-medium text-gray-900">{{ user.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-gray-500">
                                {{ user.email }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <Badge :class="getRoleColor(user.role)">
                                    {{ user.role }}
                                </Badge>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right">
                                <Link :href="route('users.edit', user.id)" class="inline-block">
                                    <Button variant="outline" size="sm">
                                        <Edit class="h-4 w-4" />
                                    </Button>
                                </Link>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div v-if="users.length === 0" class="text-center py-12">
                <p class="text-gray-500">No users found.</p>
                <Link :href="route('users.create')" class="mt-4 inline-block">
                    <Button>Add your first user</Button>
                </Link>
            </div>
        </div>
    </AuthenticatedLayout>
</template>
